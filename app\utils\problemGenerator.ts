import type {
  FunctionType,
  DisplayNotationProblem,
  DisplayDomainRangeProblem,
  NotationAnswer
} from '~/types/game'

/**
 * Generate random integer within range
 */
function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * Generate random coefficient (avoiding 0)
 */
function randomCoeff(): number {
  const coeff = randomInt(-5, 5)
  return coeff === 0 ? 1 : coeff
}









/**
 * Generate display-only notation problems for teacher assessment
 */
export function generateDisplayNotationProblem(): DisplayNotationProblem {
  const id = `display-notation-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

  const types: Array<'english' | 'set-builder' | 'interval'> = ['english', 'set-builder', 'interval']
  const type = types[randomInt(0, types.length - 1)]!

  // Define statement structures
  const statementTypes = [
    'all-real',
    'greater-than',
    'greater-equal',
    'less-than',
    'less-equal',
    'between-open',
    'between-closed',
    'between-left-closed',
    'between-right-closed'
  ]

  const statementType = statementTypes[randomInt(0, statementTypes.length - 1)]!

  let content = ''
  let description = ''

  // Generate random values once for consistency across all notation formats
  const a = randomInt(-10, 10)
  const b = randomInt(a + 1, a + 10) // Ensure b > a for intervals

  // Generate all three formats for the same mathematical concept
  const answer: NotationAnswer = {
    english: generateEnglishStatement(statementType, a, b),
    setBuilder: generateSetBuilderNotation(statementType, a, b),
    interval: generateIntervalNotation(statementType, a, b)
  }

  switch (type) {
    case 'english':
      description = 'English Statement'
      content = answer.english
      break

    case 'set-builder':
      description = 'Set-Builder Notation'
      content = answer.setBuilder
      break

    case 'interval':
      description = 'Interval Notation'
      content = answer.interval
      break
  }

  return {
    id,
    type,
    content,
    description,
    answer
  }
}

function generateEnglishStatement(statementType: string, a: number, b: number): string {
  switch (statementType) {
    case 'all-real':
      return 'all real numbers'
    case 'greater-than':
      return `all numbers greater than ${a}`
    case 'greater-equal':
      return `all numbers greater than or equal to ${a}`
    case 'less-than':
      return `all numbers less than ${a}`
    case 'less-equal':
      return `all numbers less than or equal to ${a}`
    case 'between-open':
      return `all numbers between ${a} and ${b}`
    case 'between-closed':
      return `all numbers between ${a} and ${b}, including ${a} and ${b}`
    case 'between-left-closed':
      return `all numbers between ${a} and ${b}, including ${a} but not ${b}`
    case 'between-right-closed':
      return `all numbers between ${a} and ${b}, including ${b} but not ${a}`
    default:
      return 'all real numbers'
  }
}

function generateSetBuilderNotation(statementType: string, a: number, b: number): string {
  switch (statementType) {
    case 'all-real':
      return '{x | x ∈ ℝ}'
    case 'greater-than':
      return `{x | x > ${a}}`
    case 'greater-equal':
      return `{x | x ≥ ${a}}`
    case 'less-than':
      return `{x | x < ${a}}`
    case 'less-equal':
      return `{x | x ≤ ${a}}`
    case 'between-open':
      return `{x | ${a} < x < ${b}}`
    case 'between-closed':
      return `{x | ${a} ≤ x ≤ ${b}}`
    case 'between-left-closed':
      return `{x | ${a} ≤ x < ${b}}`
    case 'between-right-closed':
      return `{x | ${a} < x ≤ ${b}}`
    default:
      return '{x | x ∈ ℝ}'
  }
}

function generateIntervalNotation(statementType: string, a: number, b: number): string {
  switch (statementType) {
    case 'all-real':
      return '(-∞, ∞)'
    case 'greater-than':
      return `(${a}, ∞)`
    case 'greater-equal':
      return `[${a}, ∞)`
    case 'less-than':
      return `(-∞, ${a})`
    case 'less-equal':
      return `(-∞, ${a}]`
    case 'between-open':
      return `(${a}, ${b})`
    case 'between-closed':
      return `[${a}, ${b}]`
    case 'between-left-closed':
      return `[${a}, ${b})`
    case 'between-right-closed':
      return `(${a}, ${b}]`
    default:
      return '(-∞, ∞)'
  }
}

/**
 * Generate display-only domain/range problems for teacher assessment
 */
export function generateDisplayDomainRangeProblem(): DisplayDomainRangeProblem {
  const id = `display-domain-range-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

  const functionTypes: FunctionType[] = ['linear', 'quadratic', 'cubic', 'absolute', 'square-root']
  const functionType = functionTypes[randomInt(0, functionTypes.length - 1)]!

  const questionTypes: Array<'domain' | 'range'> = ['domain', 'range']
  const questionType = questionTypes[randomInt(0, questionTypes.length - 1)]!

  // Generate function equation and calculate domain/range
  const functionData = generateFunctionWithDomainRange(functionType)

  return {
    id,
    functionType,
    equation: functionData.equation,
    questionType,
    description: `Find the ${questionType.charAt(0).toUpperCase() + questionType.slice(1)}`,
    answer: {
      domain: functionData.domain,
      range: functionData.range,
      domainSetBuilder: intervalToSetBuilder(functionData.domain),
      rangeSetBuilder: intervalToSetBuilder(functionData.range)
    }
  }
}

/**
 * Convert interval notation to set-builder notation
 */
function intervalToSetBuilder(interval: string): string {
  // Handle special cases
  if (interval === '(-∞, ∞)') {
    return '{x | x ∈ ℝ}'
  }

  // Parse interval notation patterns
  const patterns = [
    { regex: /^\[(-?\d+(?:\/\d+)?), ∞\)$/, template: (match: RegExpMatchArray) => `{x | x ≥ ${match[1]}}` },
    { regex: /^\((-?\d+(?:\/\d+)?), ∞\)$/, template: (match: RegExpMatchArray) => `{x | x > ${match[1]}}` },
    { regex: /^\(-∞, (-?\d+(?:\/\d+)?)\]$/, template: (match: RegExpMatchArray) => `{x | x ≤ ${match[1]}}` },
    { regex: /^\(-∞, (-?\d+(?:\/\d+)?)\)$/, template: (match: RegExpMatchArray) => `{x | x < ${match[1]}}` },
    { regex: /^\[(-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\]$/, template: (match: RegExpMatchArray) => `{x | ${match[1]} ≤ x ≤ ${match[2]}}` },
    { regex: /^\((-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\)$/, template: (match: RegExpMatchArray) => `{x | ${match[1]} < x < ${match[2]}}` },
    { regex: /^\[(-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\)$/, template: (match: RegExpMatchArray) => `{x | ${match[1]} ≤ x < ${match[2]}}` },
    { regex: /^\((-?\d+(?:\/\d+)?), (-?\d+(?:\/\d+)?)\]$/, template: (match: RegExpMatchArray) => `{x | ${match[1]} < x ≤ ${match[2]}}` }
  ]

  for (const pattern of patterns) {
    const match = interval.match(pattern.regex)
    if (match) {
      return pattern.template(match)
    }
  }

  // Fallback for unrecognized patterns
  return '{x | x ∈ ℝ}'
}

/**
 * Generate function with domain and range calculations
 */
function generateFunctionWithDomainRange(type: FunctionType): { equation: string; domain: string; range: string } {
  const a = randomCoeff()
  const b = randomInt(-5, 5)
  const c = randomInt(-5, 5)
  const h = randomInt(-3, 3)
  const k = randomInt(-3, 3)

  switch (type) {
    case 'linear':
      // f(x) = ax + b
      return {
        equation: `f(x) = ${formatCoeff(a)}x${formatConstant(b)}`,
        domain: '(-∞, ∞)',
        range: '(-∞, ∞)'
      }

    case 'quadratic': {
      // f(x) = ax² + bx + c
      // Calculate vertex using exact arithmetic to avoid floating point errors
      // vertex_x = -b/(2a), vertex_y = a*(-b/(2a))² + b*(-b/(2a)) + c
      // Simplifying: vertex_y = a*b²/(4a²) - b²/(2a) + c = b²/(4a) - b²/(2a) + c = -b²/(4a) + c
      const vertex_y_numerator = -b * b + 4 * a * c
      const vertex_y_denominator = 4 * a
      const vertex_y_fraction = formatFraction(vertex_y_numerator, vertex_y_denominator)

      return {
        equation: `f(x) = ${formatCoeff(a)}x²${formatTerm(b, 'x')}${formatConstant(c)}`,
        domain: '(-∞, ∞)',
        range: a > 0 ? `[${vertex_y_fraction}, ∞)` : `(-∞, ${vertex_y_fraction}]`
      }
    }

    case 'cubic': {
      // f(x) = ax³ + bx² + cx + d
      const d = randomInt(-5, 5)
      return {
        equation: `f(x) = ${formatCoeff(a)}x³${formatTerm(b, 'x²')}${formatTerm(c, 'x')}${formatConstant(d)}`,
        domain: '(-∞, ∞)',
        range: '(-∞, ∞)'
      }
    }

    case 'absolute':
      // f(x) = a|x - h| + k
      return {
        equation: `f(x) = ${formatCoeff(a)}|x${formatConstant(-h, true)}|${formatConstant(k)}`,
        domain: '(-∞, ∞)',
        range: a > 0 ? `[${k}, ∞)` : `(-∞, ${k}]`
      }

    case 'square-root':
      // f(x) = a√(x - h) + k
      return {
        equation: `f(x) = ${formatCoeff(a)}√(x${formatConstant(-h, true)})${formatConstant(k)}`,
        domain: `[${h}, ∞)`,
        range: a > 0 ? `[${k}, ∞)` : `(-∞, ${k}]`
      }

    default:
      return {
        equation: 'f(x) = x',
        domain: '(-∞, ∞)',
        range: '(-∞, ∞)'
      }
  }
}



/**
 * Format coefficient for display
 */
function formatCoeff(coeff: number): string {
  if (coeff === 1) return ''
  if (coeff === -1) return '-'
  return coeff.toString()
}

/**
 * Format term with coefficient
 */
function formatTerm(coeff: number, variable: string): string {
  if (coeff === 0) return ''
  if (coeff > 0) return ` + ${coeff === 1 ? '' : coeff}${variable}`
  return ` - ${coeff === -1 ? '' : Math.abs(coeff)}${variable}`
}

/**
 * Format constant term
 */
function formatConstant(constant: number, forceSign: boolean = false): string {
  if (constant === 0 && !forceSign) return ''
  if (constant === 0 && forceSign) return ` + 0`
  if (constant > 0) return ` + ${constant}`
  return ` - ${Math.abs(constant)}`
}

/**
 * Format a fraction in simplest form
 */
function formatFraction(numerator: number, denominator: number): string {
  // Handle special cases
  if (denominator === 0) return 'undefined'
  if (numerator === 0) return '0'

  // Handle negative fractions
  const isNegative = (numerator < 0) !== (denominator < 0)
  numerator = Math.abs(numerator)
  denominator = Math.abs(denominator)

  // Find GCD to simplify fraction
  const gcd = (a: number, b: number): number => b === 0 ? a : gcd(b, a % b)
  const commonDivisor = gcd(numerator, denominator)

  numerator = numerator / commonDivisor
  denominator = denominator / commonDivisor

  // Format the result
  const sign = isNegative ? '-' : ''

  if (denominator === 1) {
    return `${sign}${numerator}`
  }

  return `${sign}${numerator}/${denominator}`
}