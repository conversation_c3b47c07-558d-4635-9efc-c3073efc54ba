@import "tailwindcss";
@import "@nuxt/ui";

@theme static {
  --font-sans: "DM Sans", "sans-serif";
}

/* Math Keyboard - QWERTY Layout Styles */
/* Custom styles for the horizontal keyboard layout are handled via Tailwind classes */



/* Mathematical Typography Classes */
/* Professional mathematical expression styling similar to Microsoft Word equation editor */

.math-expression {
  font-family: 'Cambria Math', 'Latin Modern Math', 'STIX Two Math', serif;
  font-size: 1.75rem; /* 28px - much larger for classroom visibility */
  font-weight: 500; /* medium weight for clarity */
  line-height: 1.4;
  letter-spacing: 0.02em; /* slight spacing for readability */
  color: #1f2937; /* dark gray for good contrast */
}

.math-expression-large {
  font-family: 'Cambria Math', 'Latin Modern Math', 'STIX Two Math', serif;
  font-size: 2.5rem; /* 40px - very large for main problem display */
  font-weight: 600; /* semi-bold for prominence */
  line-height: 1.4;
  letter-spacing: 0.02em;
  color: #111827; /* darker for main content */
}

.math-expression-small {
  font-family: 'Cambria Math', 'Latin Modern Math', 'STIX Two Math', serif;
  font-size: 1.5rem; /* 24px - larger for secondary mathematical content */
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 0.02em;
  color: #374151; /* slightly lighter for secondary content */
}

/* Dark mode support for mathematical expressions */
.dark .math-expression {
  color: #f9fafb; /* light gray for dark backgrounds */
}

.dark .math-expression-large {
  color: #ffffff; /* pure white for main content in dark mode */
}

.dark .math-expression-small {
  color: #e5e7eb; /* slightly dimmed for secondary content in dark mode */
}

/* Special styling for mathematical operators and symbols */
.math-operator {
  margin: 0 0.15em; /* spacing around operators like +, -, =, etc. */
}

.math-parentheses {
  margin: 0 0.05em; /* minimal spacing around parentheses */
}

.math-function {
  font-style: italic; /* italicize function names like f(x) */
}

/* Interval notation specific styling */
.interval-notation {
  font-family: 'Cambria Math', 'Latin Modern Math', 'STIX Two Math', serif;
  font-size: 1.75rem; /* 28px - much larger for classroom visibility */
  font-weight: 500;
  letter-spacing: 0.03em; /* slightly more spacing for brackets and symbols */
  color: #1f2937;
}

.dark .interval-notation {
  color: #f9fafb;
}

/* Set-builder notation specific styling */
.set-builder-notation {
  font-family: 'Times New Roman', 'Cambria Math', 'Latin Modern Math', 'STIX Two Math', serif;
  font-size: 1.75rem; /* 28px - much larger for classroom visibility */
  font-weight: 500;
  letter-spacing: 0.02em;
  color: #1f2937;
}

.dark .set-builder-notation {
  color: #f9fafb;
}

/* Function equation styling */
.function-equation {
  font-family: 'Times New Roman', 'Cambria Math', 'Latin Modern Math', 'STIX Two Math', serif;
  font-size: 2.25rem; /* 36px - very prominent for function equations */
  font-weight: 600;
  line-height: 1.4;
  letter-spacing: 0.02em;
  color: #111827;
}

.dark .function-equation {
  color: #ffffff;
}

/* Answer display mathematical content */
/* Mathematical answer styling - improved contrast for classroom visibility */
.math-answer {
  font-family: 'Times New Roman', 'Cambria Math', 'Latin Modern Math', 'STIX Two Math', serif;
  font-size: 1.75rem; /* 28px - large for answer visibility */
  font-weight: 600; /* slightly bolder for answers */
  line-height: 1.4;
  letter-spacing: 0.02em;
  color: #059669; /* green tint for correct answers */
}

.dark .math-answer {
  color: #10b981; /* brighter green for dark mode */
}

/* Domain-specific mathematical notation with high contrast */
.math-answer-domain {
  font-family: 'Times New Roman', 'Cambria Math', 'Latin Modern Math', 'STIX Two Math', serif;
  font-size: 1.75rem; /* 28px - classroom-optimized size */
  font-weight: 600;
  line-height: 1.4;
  letter-spacing: 0.02em;
  color: #1e40af; /* strong blue for domain on blue background */
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8); /* subtle shadow for readability */
}

.dark .math-answer-domain {
  color: #93c5fd; /* light blue for dark mode */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Range-specific mathematical notation with high contrast */
.math-answer-range {
  font-family: 'Times New Roman', 'Cambria Math', 'Latin Modern Math', 'STIX Two Math', serif;
  font-size: 1.75rem; /* 28px - classroom-optimized size */
  font-weight: 600;
  line-height: 1.4;
  letter-spacing: 0.02em;
  color: #7c2d92; /* strong purple for range on purple background */
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8); /* subtle shadow for readability */
}

.dark .math-answer-range {
  color: #c084fc; /* light purple for dark mode */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Legacy monospace override - keep for non-mathematical code */
.font-mono:not(.math-expression):not(.math-expression-large):not(.math-expression-small):not(.interval-notation):not(.set-builder-notation):not(.function-equation):not(.math-answer) {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* Two-Column Layout Responsive Design */
/* Ensure proper spacing and alignment for classroom displays */
@media (max-width: 1023px) {
  /* On smaller screens, stack columns vertically */
  .math-expression-large {
    font-size: 2rem !important; /* 32px - slightly smaller for mobile */
  }

  .function-equation {
    font-size: 1.875rem !important; /* 30px - adjusted for mobile */
  }

  /* Ensure adequate spacing between stacked elements */
  .space-y-6 > * + * {
    margin-top: 1.5rem;
  }
}

@media (min-width: 1024px) {
  /* Ensure consistent column heights for side-by-side layout */
  .lg\:grid-cols-2 > * {
    display: flex;
    flex-direction: column;
  }

  /* Ensure answer area maintains minimum height for two-column layout */
  .min-h-\[400px\] {
    min-height: 400px;
  }
}
