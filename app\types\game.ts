// Game type definitions for the educational math application

export type GameMode = 'notation-conversion' | 'domain-range'

export type NotationType = 'algebraic' | 'interval' | 'set-builder'

export type FunctionType = 'linear' | 'quadratic' | 'cubic' | 'absolute' | 'square-root'





export interface NotationAnswer {
  english: string
  setBuilder: string
  interval: string
}

export interface DisplayNotationProblem {
  id: string
  type: 'english' | 'set-builder' | 'interval'
  content: string
  description: string // e.g., "English Statement", "Set-Builder Notation"
  answer: NotationAnswer // All three notation formats for the same mathematical concept
}

export interface DomainRangeAnswer {
  domain: string
  range: string
  domainSetBuilder: string
  rangeSetBuilder: string
}

export interface DisplayDomainRangeProblem {
  id: string
  functionType: FunctionType
  equation: string
  questionType: 'domain' | 'range'
  description: string // e.g., "Find the Domain", "Find the Range"
  answer: DomainRangeAnswer // Both domain and range for the function
}